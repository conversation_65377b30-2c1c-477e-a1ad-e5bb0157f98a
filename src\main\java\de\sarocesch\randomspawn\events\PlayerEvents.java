package de.sarocesch.randomspawn.events;

import de.sarocesch.randomspawn.RandomSpawn;
import de.sarocesch.randomspawn.config.RandomSpawnConfig;
import de.sarocesch.randomspawn.util.TeleportationHelper;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = RandomSpawn.MODID)
public class PlayerEvents {

    @SubscribeEvent
    public static void onPlayerLoggedIn(PlayerEvent.PlayerLoggedInEvent event) {
        if (event.getEntity() instanceof ServerPlayer player) {
            String playerName = player.getName().getString();

            // Check if player is in the exception list
            boolean isInExceptionList = RandomSpawnConfig.playerExceptions.contains(playerName);

            // Determine if we should teleport based on whitelist/blacklist setting
            boolean shouldTeleport = RandomSpawnConfig.useWhitelist ? isInExceptionList : !isInExceptionList;

            // Check if this is the first time the player has joined or if we should teleport on every join
            boolean isFirstJoin = !player.getPersistentData().contains("randomspawn.teleported");

            if (shouldTeleport && (isFirstJoin || RandomSpawnConfig.teleportOnEveryJoin)) {
                RandomSpawn.info("Teleporting player {} to random location", playerName);

                // Mark player as teleported
                player.getPersistentData().putBoolean("randomspawn.teleported", true);

                // Teleport the player to a random location
                TeleportationHelper.teleportToRandomLocation(player);

                // Send teleport message if enabled
                if (RandomSpawnConfig.showTeleportMessage) {
                    player.displayClientMessage(Component.literal(RandomSpawnConfig.teleportMessage), false);
                }
            }
        }
    }
}
