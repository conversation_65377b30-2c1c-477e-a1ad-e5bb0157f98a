package de.sarocesch.randomspawn.util;

import de.sarocesch.randomspawn.RandomSpawn;
import de.sarocesch.randomspawn.config.RandomSpawnConfig;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.levelgen.Heightmap;

import java.util.Random;

/**
 * Helper class for teleportation-related functionality.
 */
public class TeleportationHelper {
    // No longer needed as we use a direct approach

    /**
     * Teleports a player to a random location in an appropriate dimension.
     *
     * @param player The player to teleport
     */
    public static void teleportToRandomLocation(ServerPlayer player) {
        // Determine which dimension to teleport to
        ServerLevel targetLevel = determineTargetDimension(player);
        if (targetLevel == null) {
            RandomSpawn.error("Could not determine target dimension for teleportation");
            return;
        }

        // Get current position for logging
        double oldX = player.getX();
        double oldY = player.getY();
        double oldZ = player.getZ();

        // Get world spawn as reference point
        BlockPos spawnPos = targetLevel.getSharedSpawnPos();
        int spawnX = spawnPos.getX();
        int spawnZ = spawnPos.getZ();

        // Generate truly random coordinates directly
        Random random = new Random(System.currentTimeMillis());

        // Use a completely different approach for randomization
        int minDist = RandomSpawnConfig.minDistance;
        int maxDist = RandomSpawnConfig.maxDistance;

        // Generate random X and Z directly
        int xOffset = (random.nextBoolean() ? 1 : -1) * (minDist + random.nextInt(maxDist - minDist));
        int zOffset = (random.nextBoolean() ? 1 : -1) * (minDist + random.nextInt(maxDist - minDist));

        // Calculate final coordinates
        int x = spawnX + xOffset;
        int z = spawnZ + zOffset;

        // Set a fixed Y position for all dimensions to ensure we're above ground
        int y;

        // Decide whether to spawn high with slow falling or at a fixed safe height
        if (RandomSpawnConfig.spawnHighWithSlowFalling && targetLevel.dimension() == Level.OVERWORLD) {
            // Spawn at Y=100 with slow falling effect
            y = 100;

            // Add slow falling effect (duration: 30 seconds, amplifier: 0)
            player.addEffect(new MobEffectInstance(MobEffects.SLOW_FALLING, 30 * 20, 0));
            RandomSpawn.debug("Added slow falling effect to player for high spawn at Y=100");
        } else {
            // Use a fixed safe height based on dimension
            if (targetLevel.dimension() == Level.NETHER) {
                y = 70; // Safe height in nether
            } else if (targetLevel.dimension() == Level.END) {
                y = 90; // Safe height in end
            } else {
                y = 100; // Safe height in overworld
            }
        }

        // Log the initial position for debugging
        RandomSpawn.debug("Initial spawn position set to ({}, {}, {})", x, y, z);

        // Now scan downward to find the first solid block
        boolean foundGround = false;
        int scanY = y;
        int groundY = 0;

        // Scan down from our position to find the ground
        for (int i = 0; i < 120; i++) { // Scan up to 120 blocks down
            scanY--;
            if (scanY < 0) break; // Don't go below bedrock

            BlockPos scanPos = new BlockPos(x, scanY, z);

            // Check if this block is solid (potential ground)
            if (targetLevel.getBlockState(scanPos).isSolid() &&
                !(targetLevel.getBlockState(scanPos).getBlock() instanceof net.minecraft.world.level.block.LiquidBlock)) {

                // Check if there's space above for the player (2 blocks)
                if (targetLevel.getBlockState(scanPos.above()).isAir() &&
                    targetLevel.getBlockState(scanPos.above(2)).isAir()) {

                    groundY = scanY + 1; // Position on top of the ground block
                    foundGround = true;
                    RandomSpawn.debug("Found ground at Y={}", groundY);
                    break;
                }
            }
        }

        // If we found ground and we're not using slow falling, teleport directly above ground
        if (foundGround && !RandomSpawnConfig.spawnHighWithSlowFalling) {
            y = groundY;
            RandomSpawn.debug("Setting final Y position to ground level: {}", y);
        } else if (!foundGround) {
            // If we didn't find ground, use a safe default height
            RandomSpawn.warn("Could not find ground below position ({}, {}, {}), using safe default height", x, y, z);
        }

        RandomSpawn.debug("Generated random position at ({}, {}, {}) with offsets ({}, {})", x, y, z, xOffset, zOffset);

        // Use multiple teleportation methods to ensure the player actually gets teleported
        // First, set the player's position directly
        player.moveTo(x, y, z);

        // Then use the teleport method
        player.teleportTo(targetLevel, x, y, z, player.getYRot(), player.getXRot());

        // Force position update to clients
        player.connection.teleport(x, y, z, player.getYRot(), player.getXRot());

        // Set the player's fall distance to 0 to prevent fall damage
        player.fallDistance = 0.0F;

        // Make sure the player is not in a wall
        if (!targetLevel.getBlockState(new BlockPos(x, y, z)).isAir()) {
            RandomSpawn.warn("Player teleported into a block at ({}, {}, {}), trying to adjust position", x, y, z);
            // Try to find a safe position nearby
            for (int i = 0; i < 10; i++) {
                y++;
                if (targetLevel.getBlockState(new BlockPos(x, y, z)).isAir() &&
                    targetLevel.getBlockState(new BlockPos(x, y+1, z)).isAir()) {
                    // Found a safe position, teleport again
                    player.moveTo(x, y, z);
                    player.teleportTo(targetLevel, x, y, z, player.getYRot(), player.getXRot());
                    player.connection.teleport(x, y, z, player.getYRot(), player.getXRot());
                    RandomSpawn.debug("Adjusted player position to ({}, {}, {})", x, y, z);
                    break;
                }
            }
        }

        // Log teleportation details
        String playerName = player.getName().getString();
        RandomSpawn.debug("Teleported player {} from ({}, {}, {}) to random location: ({}, {}, {}) in dimension {}",
                playerName, oldX, oldY, oldZ, x, y, z, targetLevel.dimension().location());
        RandomSpawn.info("Teleported player {} to a random location", playerName);
    }

    /**
     * Determines which dimension to teleport the player to based on configuration and current dimension.
     *
     * @param player The player to teleport
     * @return The target dimension for teleportation
     */
    private static ServerLevel determineTargetDimension(ServerPlayer player) {
        // Default to overworld
        ServerLevel overworld = player.getServer().getLevel(Level.OVERWORLD);

        // If player is in the nether and nether teleportation is allowed, use nether
        if (player.level().dimension() == Level.NETHER && RandomSpawnConfig.allowNetherTeleport) {
            ServerLevel nether = player.getServer().getLevel(Level.NETHER);
            if (nether != null) {
                return nether;
            }
        }

        // If player is in the end and end teleportation is allowed, use end
        if (player.level().dimension() == Level.END && RandomSpawnConfig.allowEndTeleport) {
            ServerLevel end = player.getServer().getLevel(Level.END);
            if (end != null) {
                return end;
            }
        }

        // Fall back to overworld
        return overworld;
    }

    // These methods have been replaced with a more direct approach in teleportToRandomLocation
}
