package de.sarocesch.randomspawn.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import de.sarocesch.randomspawn.RandomSpawn;
import de.sarocesch.randomspawn.config.RandomSpawnConfig;
import de.sarocesch.randomspawn.util.TeleportationHelper;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;

import java.util.Collection;

/**
 * Registers and handles commands for the RandomSpawn mod.
 */
public class RandomSpawnCommands {

    /**
     * Registers all commands for the mod.
     *
     * @param dispatcher The command dispatcher to register commands with
     */
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(
            Commands.literal("newspawn")
                .requires(source -> source.hasPermission(2)) // Requires permission level 2 (op)
                .then(Commands.argument("players", EntityArgument.players())
                    .executes(context -> respawnPlayers(context.getSource(), EntityArgument.getPlayers(context, "players")))
                )
                .executes(context -> respawnPlayer(context.getSource(), context.getSource().getPlayerOrException()))
        );

        RandomSpawn.info("Registered RandomSpawn commands");
    }

    /**
     * Teleports multiple players to random locations.
     *
     * @param source The command source
     * @param players Collection of players to teleport
     * @return The number of players successfully teleported
     */
    private static int respawnPlayers(CommandSourceStack source, Collection<ServerPlayer> players) {
        int successCount = 0;
        final int totalCount = players.size();

        for (ServerPlayer player : players) {
            if (respawnPlayer(source, player) == 1) {
                successCount++;
            }
        }

        // Summary message for multiple players
        if (totalCount > 1) {
            final int finalSuccessCount = successCount;
            if (successCount == totalCount) {
                source.sendSuccess(() -> Component.literal("Successfully teleported all " + totalCount + " players"), true);
            } else {
                source.sendSuccess(() -> Component.literal("Teleported " + finalSuccessCount + " out of " + totalCount + " players"), true);
            }
        }

        return successCount;
    }

    /**
     * Teleports a player to a random location.
     *
     * @param source The command source
     * @param player The player to teleport
     * @return 1 if successful, 0 otherwise
     */
    private static int respawnPlayer(CommandSourceStack source, ServerPlayer player) {
        // Get the player's name
        String playerName = player.getName().getString();

        // Teleport the player - in this version teleportTo is void, so we assume it always works
        TeleportationHelper.teleportToRandomLocation(player);

        // Notify the command source (only for single player teleports or self-teleport)
        if (player == source.getEntity() || source.getLevel().getPlayers(p -> true).size() <= 2) {
            source.sendSuccess(() -> Component.literal("Teleported " + playerName + " to a random location"), true);
        }

        // Notify the player if they're not the command source
        if (player != source.getEntity() && RandomSpawnConfig.showTeleportMessage) {
            player.displayClientMessage(Component.literal(RandomSpawnConfig.teleportMessage), false);
        }

        return 1;
    }
}
