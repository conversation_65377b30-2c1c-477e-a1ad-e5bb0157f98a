package de.sarocesch.randomspawn;

import com.mojang.logging.LogUtils;
import de.sarocesch.randomspawn.commands.RandomSpawnCommands;
import de.sarocesch.randomspawn.config.RandomSpawnConfig;
import de.sarocesch.randomspawn.events.PlayerEvents;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

@Mod(RandomSpawn.MODID)
public class RandomSpawn
{
    // Define mod id in a common place for everything to reference
    public static final String MODID = "randomspawn";
    // Directly reference a slf4j logger
    private static final Logger LOGGER = LogUtils.getLogger();

    /**
     * Logs a debug message if debug mode is enabled in the configuration.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void debug(String message, Object... args) {
        if (de.sarocesch.randomspawn.config.RandomSpawnConfig.debugMode) {
            LOGGER.info(message, args);
        }
    }

    /**
     * Logs an info message regardless of debug mode.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void info(String message, Object... args) {
        LOGGER.info(message, args);
    }

    /**
     * Logs a warning message regardless of debug mode.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void warn(String message, Object... args) {
        LOGGER.warn(message, args);
    }

    /**
     * Logs an error message regardless of debug mode.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void error(String message, Object... args) {
        LOGGER.error(message, args);
    }

    public RandomSpawn()
    {
        info("Initializing Saro's Random Spawn Mod");

        // Get the mod event bus
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();

        // Register ourselves for server and other game events we are interested in
        MinecraftForge.EVENT_BUS.register(this);

        // Register the configuration
        ModLoadingContext.get().registerConfig(ModConfig.Type.COMMON, RandomSpawnConfig.SPEC);
        info("Configuration registered");

        // The PlayerEvents class is automatically registered via @Mod.EventBusSubscriber
        info("Player events registered for random teleportation");

        info("Saro's Random Spawn Mod initialized");
    }

    /**
     * Event handler for registering commands
     */
    @SubscribeEvent
    public void onRegisterCommands(RegisterCommandsEvent event) {
        debug("Registering commands");
        RandomSpawnCommands.register(event.getDispatcher());
    }
}
