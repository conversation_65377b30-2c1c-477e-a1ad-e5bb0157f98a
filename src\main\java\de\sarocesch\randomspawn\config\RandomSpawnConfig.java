package de.sarocesch.randomspawn.config;

import de.sarocesch.randomspawn.RandomSpawn;
import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.config.ModConfigEvent;

import java.util.Arrays;
import java.util.List;

@Mod.EventBusSubscriber(modid = RandomSpawn.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class RandomSpawnConfig {
    private static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();

    // General settings category
    public static final ForgeConfigSpec.IntValue MIN_DISTANCE;
    public static final ForgeConfigSpec.IntValue MAX_DISTANCE;
    public static final ForgeConfigSpec.BooleanValue TELEPORT_ON_EVERY_JOIN;
    public static final ForgeConfigSpec.BooleanValue ALLOW_NETHER_TELEPORT;
    public static final ForgeConfigSpec.BooleanValue ALLOW_END_TELEPORT;

    // Messages category
    public static final ForgeConfigSpec.BooleanValue SHOW_TELEPORT_MESSAGE;
    public static final ForgeConfigSpec.ConfigValue<String> TELEPORT_MESSAGE;

    // Safety settings
    public static final ForgeConfigSpec.BooleanValue SAFE_TELEPORT;
    public static final ForgeConfigSpec.BooleanValue SPAWN_HIGH_WITH_SLOW_FALLING;

    // Debug settings
    public static final ForgeConfigSpec.BooleanValue DEBUG_MODE;

    // Player exceptions category
    public static final ForgeConfigSpec.BooleanValue USE_WHITELIST;
    public static final ForgeConfigSpec.ConfigValue<List<? extends String>> PLAYER_EXCEPTIONS;

    public static final ForgeConfigSpec SPEC;

    static {
        BUILDER.comment("Saro's Random Spawn Configuration").push("general");

        MIN_DISTANCE = BUILDER
                .comment("Minimum distance from spawn to teleport players (in blocks)")
                .defineInRange("minDistance", 500, 0, 10000);

        MAX_DISTANCE = BUILDER
                .comment("Maximum distance from spawn to teleport players (in blocks)")
                .defineInRange("maxDistance", 2000, 100, 30000);

        TELEPORT_ON_EVERY_JOIN = BUILDER
                .comment("Whether to teleport players on every join, not just the first time")
                .define("teleportOnEveryJoin", false);

        ALLOW_NETHER_TELEPORT = BUILDER
                .comment("Whether to allow teleportation in the Nether dimension")
                .define("allowNetherTeleport", false);

        ALLOW_END_TELEPORT = BUILDER
                .comment("Whether to allow teleportation in the End dimension")
                .define("allowEndTeleport", false);

        BUILDER.pop();

        BUILDER.comment("Message Settings").push("messages");

        SHOW_TELEPORT_MESSAGE = BUILDER
                .comment("Whether to show a message to players when they are teleported")
                .define("showTeleportMessage", false);

        TELEPORT_MESSAGE = BUILDER
                .comment("Message to show to players when they are teleported")
                .define("teleportMessage", "You have been teleported to a random location!");

        BUILDER.pop();

        BUILDER.comment("Safety Settings").push("safety");

        SAFE_TELEPORT = BUILDER
                .comment("If true, ensures players are teleported to safe locations (not in lava, caves, etc.)")
                .define("safeTeleport", true);

        SPAWN_HIGH_WITH_SLOW_FALLING = BUILDER
                .comment("If true, players will spawn at Y=100 with slow falling effect and gently float down to the ground")
                .define("spawnHighWithSlowFalling", false);

        BUILDER.pop();

        BUILDER.comment("Debug Settings").push("debug");

        DEBUG_MODE = BUILDER
                .comment("If true, enables detailed debug logging to help diagnose issues")
                .define("debugMode", false);

        BUILDER.pop();

        BUILDER.comment("Player Exception Settings").push("exceptions");

        USE_WHITELIST = BUILDER
                .comment("If true, only players in the list will be teleported. If false, players in the list will NOT be teleported.")
                .define("useWhitelist", false);

        PLAYER_EXCEPTIONS = BUILDER
                .comment("List of player names to whitelist or blacklist from teleportation")
                .defineList("playerExceptions", Arrays.asList("ExamplePlayer1", "ExamplePlayer2"),
                        entry -> entry instanceof String);

        BUILDER.pop();

        SPEC = BUILDER.build();
    }

    // Config values that will be used in the code
    public static int minDistance;
    public static int maxDistance;
    public static boolean teleportOnEveryJoin;
    public static boolean allowNetherTeleport;
    public static boolean allowEndTeleport;
    public static boolean showTeleportMessage;
    public static String teleportMessage;
    public static boolean safeTeleport;
    public static boolean spawnHighWithSlowFalling;
    public static boolean debugMode;
    public static boolean useWhitelist;
    public static List<? extends String> playerExceptions;

    @SubscribeEvent
    public static void onLoad(final ModConfigEvent event) {
        // Update the local variables when the config is loaded or reloaded
        minDistance = MIN_DISTANCE.get();
        maxDistance = MAX_DISTANCE.get();
        teleportOnEveryJoin = TELEPORT_ON_EVERY_JOIN.get();
        allowNetherTeleport = ALLOW_NETHER_TELEPORT.get();
        allowEndTeleport = ALLOW_END_TELEPORT.get();
        showTeleportMessage = SHOW_TELEPORT_MESSAGE.get();
        teleportMessage = TELEPORT_MESSAGE.get();
        safeTeleport = SAFE_TELEPORT.get();
        spawnHighWithSlowFalling = SPAWN_HIGH_WITH_SLOW_FALLING.get();
        debugMode = DEBUG_MODE.get();
        useWhitelist = USE_WHITELIST.get();
        playerExceptions = PLAYER_EXCEPTIONS.get();
    }
}
